@use '../../utils/' as * ;

/*============= BLOG CSS AREA ===============*/
// Homepage 01 //
.vl-blog-1-area {
    position: relative;
    z-index: 1;
    .vl-blog-1-item {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;

            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 16px;
                transition: all .4s;
                @media #{$xs} {
                    height: 500px;
                }
                @media #{$md} {
                    height: 500px;
                }
            }
        }
        .vl-blog-1-content {
            position: absolute;
            z-index: 2;
            padding: 32px;
            border-radius: 16px;
            background: var(--ztc-bg-bg-1);
            bottom: 24px;
            left: 32px;
            right: 36px;
            @media #{$xs} {
                left: 16px;
                right: 16px;
                padding: 16px;
            }
            @media #{$md} {
                left: 16px;
                right: 16px;
            }
            ul {
                li {
                    display: inline-block;
                    &:nth-child(2) {
                        @media #{$xs} {
                            margin-top: 10px;
                        }
                        @media #{$md} {
                            margin-top: 10px;
                        }
                    } 
                    a {
                        color: var(--ztc-text-text-2);
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s18);
                        font-style: normal;
                        font-weight: var(--ztc-weight-semibold);
                        line-height: 18px;
                        display: inline-block;
                        transition: all .4s; 
                        img {
                            margin: -5px 4px 0 0;
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s24);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: #35169E;
                        transition: all .4s;
                    }                  
                }
            }
            p {
                color: var(--ztc-text-text-3);
                font-family: var(---ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 24px;              
            }
            .vl-blog-1-icon {
                position: absolute;
                right: -20px;
                top: -20px;
                a {
                    height: 56px;
                    width: 56px;
                    text-align: center;
                    line-height: 56px;
                    border-radius: 50%;
                    transition: all .4s;
                    background: var(--Main-Color, linear-gradient(90deg, #2E0797 0%, #726EFC 100%));
                    color: var(--ztc-text-text-1);
                    display: inline-block;
                    font-size: var(--ztc-font-size-font-s24);
                    transform: rotate(-45deg);
                }
            }
        }
    }
}

// Homepage 02 //
.vl-blog-2-area {
    position: relative;
    z-index: 1;
    // background-image: url(../../../img/all-images/bg/footer-bg2.png);
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
    .vl-blog-1-item {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;

            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 16px;
                transition: all .4s;
            }
        }
        .vl-blog-1-content {
            position: relative;
            z-index: 2;
            padding: 24px;
            border-radius: 16px;
            background: #0B2C36;
            margin: -100px 16px 0;
            ul {
                li {
                    display: inline-block;
                    a {
                        color: var(--ztc-text-text-1);
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                        display: inline-block;
                        transition: all .4s; 
                        border-radius: 4px;
                        background: rgba(255, 255, 255, 0.10);
                        padding: 6px 10px;                       
                        img {
                            margin: -5px 4px 0 0;
                            filter: brightness(0) invert(1);
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-1);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: var(--ztc-text-text-5);
                        transition: all .4s;
                    }                  
                }
            }
            .readmore {
                color: var(--ztc-text-text-1);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height:16px;
                    display: inline-block;
                    transition: all .4s; 
                    i {
                        margin-left: 4px;
                        transform: rotate(-45deg);
                        transition: all .4s;
                    }
                    &:hover {
                        color: var(--ztc-text-text-5);
                        transition: all .4s;
                    } 
            }
        }
    }
}

// Homepage 03 //
.features3-section-area {
    position: relative;
    z-index: 1;
    .images-area {
        position: relative;
        z-index: 1;
        .elements28 {
            position: absolute;
            top: -22px;
            left: -22px;
            z-index: 1;
        }
        svg {
            position: absolute;
            top: -120px;
            left: 33px;
            right: 0;
            width: 600px;
            height: 700px;
            @media #{$xs} {
                width: 100%;
                height: 100%;
                top: 0;
            }
        }
        .img1 {
            background: #EBE6F5;
            padding: 16px;
            border-radius: 8px;
        }
    }
    .content-area {
        padding: 0 0 0 70px;
        @media #{$xs} {
            margin-top: 30px;
            padding: 0;
        }
        @media #{$md} {
            margin-top: 30px;
            padding: 0;
        }
        h3 {
            color: var(--ztc-text-text-7);
            font-family: var(---ztc-family-font1);
            font-size: var(--ztc-font-size-font-s32);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 40px;
                  
        }
    }
    .content-area2 {
        padding: 0  70px 0 0;
        @media #{$xs} {
            margin-top: 30px;
            padding: 0;
        }
        @media #{$md} {
            margin-top: 30px;
            padding: 0;
        }
        h3 {
            color: var(--ztc-text-text-7);
            font-family: var(---ztc-family-font1);
            font-size: var(--ztc-font-size-font-s32);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 40px;
                  
        }
    }
}

// Homepage 04 //
.vl-blog-4-area {
    position: relative;
    z-index: 1;
    .elements39 {
        position: absolute;
        right: 0;
        top: 0;
    }
    .vl-blog-1-item {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;

            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 16px;
                transition: all .4s;
            }
        }
        .vl-blog-1-content {
            position: relative;
            z-index: 2;
            padding: 24px;
            border-radius: 8px;
            background: #FFF;
            border: 1px solid rgba(170, 170, 170, 0.09); 
            margin: -100px 16px 0;
            ul {
                li {
                    display: inline-block;
                    a {
                        color: var(--ztc-text-text-12);
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                        display: inline-block;
                        transition: all .4s; 
                        border-radius: 4px;
                        background: linear-gradient(0deg, rgba(32, 44, 211, 0.10) 0%, rgba(32, 44, 211, 0.10) 100%);
                        padding: 6px 10px;                       
                        img {
                            margin: -5px 4px 0 0;
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-10);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: var(--ztc-text-text-12);
                        transition: all .4s;
                    }                  
                }
            }
            .readmore {
                color: var(--ztc-text-text-10);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height:16px;
                    display: inline-block;
                    transition: all .4s; 
                    i {
                        margin-left: 4px;
                        transform: rotate(-45deg);
                        transition: all .4s;
                    }
                    &:hover {
                        color: var(--ztc-text-text-12);
                        transition: all .4s;
                    } 
            }
        }
    }
}


// Inner Pages//
.vl-blog-4-area-inner {
    position: relative;
    z-index: 1;
    .elements39 {
        position: absolute;
        right: 0;
        top: 0;
    }
    .vl-blog-1-item {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 8px;
                transition: all .4s;
            }
        }
        .vl-blog-1-content {
            position: relative;
            z-index: 2;
            padding: 24px;
            border-radius: 8px;
            background: #FFF;
            border: 1px solid rgba(170, 170, 170, 0.09); 
            margin: -100px 16px 0;
            ul {
                li {
                    display: inline-block;
                    a {
                        color: #2E0797 ;
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                        display: inline-block;
                        transition: all .4s; 
                        border-radius: 4px;
                        background: rgba(111, 105, 247, 0.20);
                        padding: 6px 10px;                       
                        img {
                            margin: -5px 4px 0 0;
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: #2E0797;
                        transition: all .4s;
                    }                  
                }
            }
            .readmore {
                color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 18px;
                    display: inline-block;
                    transition: all .4s; 
                    i {
                        margin-left: 4px;
                        transform: rotate(-45deg);
                        transition: all .4s;
                    }
                    &:hover {
                        color: #2E0797;
                        transition: all .4s;
                    } 
            }
        }
    }
}

.vl-blog-v1-area {
    position: relative;
    z-index: 1;
    .blog-v1-leftside {
        h3 {
            color: var(--ztc-text-text-2);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s24);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 24px;   
        }
        .search-area {
            position: relative;
            z-index: 1;
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px;   
            form {
                position: relative;
                z-index: 1;
                input {
                    width: 100%;
                    border-radius: 8px;
                    background: var(--ztc-bg-bg-1);
                    color: var(--Text-Color, #050734);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    padding: 19px 16px;         
                }
                button {
                    border: none;
                    background: none;
                    outline: none;
                    position: absolute;
                    right: 12px;
                    top: 12px;
                    font-size: var(--ztc-font-size-font-s22);
                }
            }     
        }
        .category-list-area {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            ul {
                li {
                    margin-top: 18px;
                    a {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        color: var(--Text-Color, #050734);
                        font-family: var(--ztc-family-font1);
                        font-size:  var(--ztc-font-size-font-s18);
                        font-style: normal;
                        font-weight: var(--ztc-weight-semibold);
                        line-height: 18px; 
                        border-radius: 8px;
                        background: var(--ztc-bg-bg-1);    
                        padding: 20px;
                        transition: all .4s; 
                        position: relative;
                        z-index: 1;
                        &:hover {
                            transition: all .4s;
                            color: var(--ztc-text-text-1);
                            &::after {
                                visibility: visible;
                                opacity: 1;
                                transition: all .4s;
                            }
                        }
                        &::after {
                            position: absolute;
                            content: "";
                            width: 100%;
                            height: 100%;
                            left: 0;
                            top: 0;
                            background: var(--ztc-bg-bg-5);
                            transition: all .4s;
                            border-radius: 8px;
                            z-index: -1;
                            visibility: hidden;
                            opacity: 0;
                        }       
                    }
                }
            }  
        }
        .tags-area {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            ul{
                li {
                    display: inline-block;
                    a {
                        font-family: var(--ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s18);
                        line-height: 18px;
                        display: inline-block;
                        border-radius: 4px;
                        background: var(--ztc-bg-bg-1);
                        padding: 10px;
                        font-weight: var(--ztc-weight-semibold);
                        transition: all .4s;
                        color: var(--ztc-text-text-2);
                        margin-top: 16px;
                        position: relative;
                        z-index: 1;
                        margin-right: 12px;
                        @media #{$xs} {
                            margin-right: 0;
                        }
                        &:hover {
                            transition: all .4s;
                            color: var(--ztc-text-text-1);
                            &::after {
                                visibility: visible;
                                opacity: 1;
                                transition: all .4s;
                            }
                        }
                        &::after {
                            position: absolute;
                            content: "";
                            width: 100%;
                            height: 100%;
                            left: 0;
                            top: 0;
                            background: var(--ztc-bg-bg-5);
                            transition: all .4s;
                            border-radius: 8px;
                            z-index: -1;
                            visibility: hidden;
                            opacity: 0;
                        } 
                    }
                }
            }
        }
        .auhtor-area {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            ul {
                li {
                    display: inline-block;
                    a {
                        margin: 0 10px 0 0;
                        img {
                            height: 70px;
                            width: 70px;
                            text-align: center;
                            line-height: 70px;
                            border-radius: 50%;
                            object-fit: cover;
                        }
                    }
                }
            }
        }
    }
    .vl-blog-1-item {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
                .vl-blog-1-content  {
                    ul {
                        li {
                            a {
                                background: var(--ztc-bg-bg-5);
                                transition: all .4s;
                                color: var(--ztc-text-text-1);
                                svg {
                                    filter: brightness(0) invert(1);
                                    transition: all .4s;
                                }
                            }
                        }
                    }
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 8px;
                transition: all .4s;
            }
        }
        .vl-blog-1-content {
            position: relative;
            z-index: 2;
            padding: 24px;
            border-radius: 8px;
            background: #FFF;
            border: 1px solid rgba(170, 170, 170, 0.09); 
            margin: -100px 16px 0;
            ul {
                li {
                    display: inline-block;
                    a {
                        color: #2E0797 ;
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                        display: inline-block;
                        transition: all .4s; 
                        border-radius: 4px;
                        background: rgba(111, 105, 247, 0.20);
                        padding: 6px 10px;                       
                        img {
                            margin: -5px 4px 0 0;
                            transition: all .4s;
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: #2E0797;
                        transition: all .4s;
                    }                  
                }
            }
            .readmore {
                color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 18px;
                    display: inline-block;
                    transition: all .4s; 
                    i {
                        margin-left: 4px;
                        transform: rotate(-45deg);
                        transition: all .4s;
                    }
                    &:hover {
                        color: #2E0797;
                        transition: all .4s;
                    } 
            }
        }
    }

    .vl-blog-1-item1 {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;

            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 16px;
                transition: all .4s;
                @media #{$xs} {
                    height: 500px;
                }
                @media #{$md} {
                    height: 500px;
                }
            }
        }
        .vl-blog-1-content {
            position: absolute;
            z-index: 2;
            padding: 32px;
            border-radius: 16px;
            background: var(--ztc-bg-bg-1);
            bottom: 24px;
            left: 32px;
            right: 36px;
            @media #{$xs} {
                left: 16px;
                right: 16px;
                padding: 16px;
            }
            @media #{$md} {
                left: 16px;
                right: 16px;
            }
            ul {
                li {
                    display: inline-block;
                    &:nth-child(2) {
                        @media #{$xs} {
                            margin-top: 10px;
                        }
                        @media #{$md} {
                            margin-top: 10px;
                        }
                    } 
                    a {
                        color: var(--ztc-text-text-2);
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s18);
                        font-style: normal;
                        font-weight: var(--ztc-weight-semibold);
                        line-height: 18px;
                        display: inline-block;
                        transition: all .4s; 
                        img {
                            margin: -5px 4px 0 0;
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s24);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: #35169E;
                        transition: all .4s;
                    }                  
                }
            }
            p {
                color: var(--ztc-text-text-3);
                font-family: var(---ztc-family-font1);
                font-size: var(--ztc-font-size-font-s16);
                font-style: normal;
                font-weight: var(--ztc-weight-medium);
                line-height: 24px;              
            }
            .vl-blog-1-icon {
                position: absolute;
                right: -20px;
                top: -20px;
                a {
                    height: 56px;
                    width: 56px;
                    text-align: center;
                    line-height: 56px;
                    border-radius: 50%;
                    transition: all .4s;
                    background: var(--Main-Color, linear-gradient(90deg, #2E0797 0%, #726EFC 100%));
                    color: var(--ztc-text-text-1);
                    display: inline-block;
                    font-size: var(--ztc-font-size-font-s24);
                    transform: rotate(-45deg);
                }
            }
        }
    }
}

.vl-blog-details-section {
    position: relative;
    z-index: 1;
    .blog-auhtor-details-side {
        h3 {
            color: var(--ztc-text-text-2);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s24);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 24px;   
        }
        .search-area {
            position: relative;
            z-index: 1;
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px;   
            form {
                position: relative;
                z-index: 1;
                input {
                    width: 100%;
                    border-radius: 8px;
                    background: var(--ztc-bg-bg-1);
                    color: var(--Text-Color, #050734);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    padding: 19px 16px;         
                }
                button {
                    border: none;
                    background: none;
                    outline: none;
                    position: absolute;
                    right: 12px;
                    top: 12px;
                    font-size: var(--ztc-font-size-font-s22);
                }
            }     
        }
        .category-list-area {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            ul {
                li {
                    margin-top: 18px;
                    a {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        color: var(--Text-Color, #050734);
                        font-family: var(--ztc-family-font1);
                        font-size:  var(--ztc-font-size-font-s18);
                        font-style: normal;
                        font-weight: var(--ztc-weight-semibold);
                        line-height: 18px; 
                        border-radius: 8px;
                        background: var(--ztc-bg-bg-1);    
                        padding: 20px;
                        transition: all .4s; 
                        position: relative;
                        z-index: 1;
                        &:hover {
                            transition: all .4s;
                            color: var(--ztc-text-text-1);
                            &::after {
                                visibility: visible;
                                opacity: 1;
                                transition: all .4s;
                            }
                        }
                        &::after {
                            position: absolute;
                            content: "";
                            width: 100%;
                            height: 100%;
                            left: 0;
                            top: 0;
                            background: var(--ztc-bg-bg-5);
                            transition: all .4s;
                            border-radius: 8px;
                            z-index: -1;
                            visibility: hidden;
                            opacity: 0;
                        }       
                    }
                }
            }  
        }
        .tags-area {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            @media #{$xs} {
                text-align: center;
            }
            ul{
                li {
                    display: inline-block;
                    a {
                        font-family: var(--ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s18);
                        line-height: 18px;
                        display: inline-block;
                        border-radius: 4px;
                        background: var(--ztc-bg-bg-1);
                        padding: 10px;
                        font-weight: var(--ztc-weight-semibold);
                        transition: all .4s;
                        color: var(--ztc-text-text-2);
                        margin-top: 16px;
                        position: relative;
                        z-index: 1;
                        margin-right: 12px;
                        @media #{$xs} {
                            margin-right: 0;
                        }
                        &:hover {
                            transition: all .4s;
                            color: var(--ztc-text-text-1);
                            &::after {
                                visibility: visible;
                                opacity: 1;
                                transition: all .4s;
                            }
                        }
                        &::after {
                            position: absolute;
                            content: "";
                            width: 100%;
                            height: 100%;
                            left: 0;
                            top: 0;
                            background: var(--ztc-bg-bg-5);
                            transition: all .4s;
                            border-radius: 8px;
                            z-index: -1;
                            visibility: hidden;
                            opacity: 0;
                        } 
                    }
                }
            }
        }
        .auhtor-area {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            ul {
                li {
                    display: inline-block;
                    a {
                        margin: 0 10px 0 0;
                        img {
                            height: 70px;
                            width: 70px;
                            text-align: center;
                            line-height: 70px;
                            border-radius: 50%;
                            object-fit: cover;
                        }
                    }
                }
            }
        }

        .recent-posts-area {
            position: relative;
            z-index: 1;
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px;  
            @media #{$xs} {
                text-align: center;
            } 
            .recent-posts {
                position: relative;
                @media #{$xs} {
                    text-align: center;
                }
                .img1 {
                    position: absolute;
                    @media #{$xs} {
                        position: relative;
                    }
                    img {
                        height: 100%;
                        width: 100%;
                        object-fit: cover;
                        border-radius: 4px;
                        @media #{$xs} {
                            height: 100px;
                            width: 100px;
                            object-fit: cover;
                        }
                    }
                }

                .content {
                    padding-left: 140px;
                    @media #{$xs} {
                        padding-left: 0;
                        margin-top: 16px;
                    }
                    ul {
                        li {
                            a {
                                color: #2E0797 ;
                                font-family: var(---ztc-family-font1);
                                font-size: var(--ztc-font-size-font-s16);
                                font-style: normal;
                                font-weight: var(--ztc-weight-medium);
                                line-height: 16px;
                                display: inline-block;
                                transition: all .4s; 
                                border-radius: 4px;
                                background: rgba(111, 105, 247, 0.20);
                                padding: 6px 10px;                       
                                img {
                                    margin: -5px 4px 0 0;
                                    transition: all .4s;
                                } 
                                span {
                                    color: #E6E6EB;
                                    display: inline-block;
                                    margin: 0 8px 0;
                                }                     
                            } 
                        }
                    }
                    h4  {
                        a {
                            color: var(--ztc-text-text-2);
                            font-family: var(---ztc-family-font1);
                            font-size: var(--ztc-font-size-font-s18);
                            font-style: normal;
                            font-weight: var(--ztc-weight-semibold);
                            line-height: 26px;
                            display: inline-block;
                            transition: all .4s; 
                            &:hover {
                                color: #2E0797;
                                transition: all .4s;
                            }                  
                        }
                    }
                }
            }      
        }
    }

    .blog-others-sidebar {
        position: relative;
        z-index: 1;
        padding: 0 0 0 70px;
        &.rightside {
            padding: 0 70px 0 0 !important;
            @media #{$xs} {
                padding: 0 !important;
                margin-bottom: 30px;
            }
            @media #{$md} {
                padding: 0 !important;
                margin-bottom: 30px;
            }
        }
        @media #{$xs} {
            padding: 0;
            margin-top: 30px;
        }
        @media #{$md} {
            padding: 0;
            margin-top: 30px;
        }
        h2 {
            color: var(--ztc-text-text-2);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s38);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 48px;    
            @media #{$xs} {
                font-size: var(--ztc-font-size-font-s32);
                line-height: 40px;
            }    
        }
        h3 {
            color: var(--ztc-text-text-2);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s32);
            font-style: normal;
            font-weight: var(--ztc-weight-semibold);
            line-height: 40px; 
        }
        p {
            color: var(--ztc-text-text-3);
            font-family: var(--ztc-family-font1);
            font-size: var(--ztc-font-size-font-s18);
            font-style: normal;
            font-weight: var(--ztc-weight-medium);
            line-height: 26px;
            letter-spacing: -0.18px;       
        }
        .img1  {
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 8px;
            }
        }
        .list-author {
            li {
                display: inline-block;
                &:nth-child(3) {
                    @media #{$xs} {
                        margin-top: 10px;
                    }
                }
                &:nth-child(1) {
                    a {
                        color: var(--ztc-text-text-1);
                        font-family: var(--ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var
                        --ztc-weight-medium;
                        line-height: 16px;
                        text-transform: capitalize;  
                        display: inline-block;
                        background: var(--ztc-bg-bg-5);
                        padding: 10px;
                        border-radius: 4px;   
                        margin: 0 16px 0 0;         
                    }
                }
                 a {
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    display: inline-block;
                    img {
                        height: 18px;
                        width: 18px;
                        object-fit: contain;
                        margin: -5px 4px 0 0;
                    }
                    span {
                        color: #CDCDD6;
                        display: inline-block;
                        margin: 0 8px;
                    }
                }
            }
        }
        .images {
            position: relative;
            z-index: 1;
            .img1 {
                &::after {
                    position: absolute;
                    content: "";
                    height: 100%;
                    width: 100%;
                    object-fit: cover;
                    background: var(--ztc-bg-bg-2);
                    transition: all .4s;
                    opacity: 30%;
                    top: 0;
                    left: 0;
                    transform: rotate(0);
                    border-radius: 8px;
                }
            }
            .play {
                position: absolute;
                left: 50%;
                top: 50%;
                margin-left: -40px;
                margin-top: -40px;
                z-index: 2;
                a {
                    height: 80px;
                    width: 80px;
                    text-align: center;
                    line-height: 80px;
                    border-radius: 50%;
                    display: inline-block;
                    transition: all .4s;
                    background: var(--ztc-bg-bg-5);
                    color: var(--ztc-text-text-1);
                    font-size: var(--ztc-font-size-font-s32);
                    position: relative;
                    &::after {
                        position: absolute;
                        content: "";
                        height: 100%;
                        width: 100%;
                        left: 40px;
                        top: 40px;
                        background: var(--ztc-bg-bg-5);
                        transition: all .4s;
                        z-index: -1;
                        border-radius: 50%;
                        animation: pulse-border 1500ms ease-out infinite;
                        opacity: 40%;
                        display: inline-block;
                    }
                }
            }
        }
        .tags-social {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 32px;
            border-top: 1px solid #E6E6EB;
            @media #{$xs} {
                display: inline-block;
            }
            .tags {
                @media #{$xs} {
                    margin-bottom: 16px;
                }
                ul {
                    li {
                        display: inline-block;
                        &:nth-child(1) {
                            color: var(--ztc-text-text-2);
                            font-family: var(--ztc-family-font1);
                            font-size: var(--ztc-font-size-font-s24);
                            font-style: normal;
                            font-weight: var(--ztc-weight-semibold);
                            line-height: 24px;
                            margin: 0 16px 0 0;            
                        }
                        a {
                            color: var(--ztc-text-text-2);
                            font-family: var(--ztc-family-font1);
                            font-size: var(--ztc-font-size-font-s16);
                            font-style: normal;
                            font-weight: var(--ztc-weight-medium);
                            line-height: 16px; 
                            text-transform: capitalize;  
                            display: inline-block;
                            transition: all .4s;  
                            border-radius: 4px;
                            background: var(--Gray-Color, #EFF1FF);
                            padding: 10px;   
                            margin: 0 8px 0 0;
                            &:hover {
                                background: var(--ztc-bg-bg-5);
                                color: var(--ztc-text-text-1);
                                transition: all .4s;
                            }     
                        }
                    }
                }
            }
            .social {
                ul {
                    li {
                        &:nth-child(1) {
                            color: var(--ztc-text-text-2);
                            font-family: var(--ztc-family-font1);
                            font-size: var(--ztc-font-size-font-s24);
                            font-style: normal;
                            font-weight: var(--ztc-weight-semibold);
                            line-height: 24px;
                            margin: 0 16px 0 0;            
                        }
                        display: inline-block;
                        a {
                            height: 44px;
                            width: 44px;
                            text-align: center;
                            line-height: 44px;
                            border-radius: 50%;
                            transition: all .4s;
                            background: #EFF1FF;
                            color: var(--ztc-text-text-2);
                            transition: all .4s;
                            display: inline-block;
                            font-size: var(--ztc-font-size-font-s20);
                            margin: 0 8px 0 0;
                            &:hover {
                                background: var(--ztc-bg-bg-5);
                                transition: all .4s;
                                color: var(--ztc-text-text-1);
                            }
                        }
                    }
                }
            }
        }
        .comments-boxarea {
            position: relative;
            z-index: 1;
            background: #EFF1FF;
            border-radius: 4px;
            padding: 24px;  
            &.box2 {
                margin: 0 0 0 30px;
                @media #{$xs} {
                    margin: 0;
                }
                @media #{$md} {
                    margin: 0;
                }
            } 
              .comments-boxes {
                display: flex;
                align-items: center;
                justify-content: space-between;
                @media #{$xs} {
                    display: inline-block;
                }
                .comments-auhtor-box {
                    display: flex;
                    align-items: center;
                    .img3 {
                        img {
                            height: 80px;
                            width: 80px;
                            border-radius: 50%;
                            object-fit: cover;
                        }
                    }
                    .content {
                        padding-left: 18px;
                        .date {
                            color: var(--ztc-text-text-3);
                            font-family: var(--ztc-family-font1);
                            font-size: var(--ztc-font-size-font-s16);
                            font-style: normal;
                            font-weight: var(--ztc-weight-medium);
                            line-height: 16px; /* 100% */ 
                            display: inline-block;
                            margin-bottom: 16px;
                            img {
                                height: 18px;
                                width: 18px;
                                object-fit: contain;
                                margin: -5px 4px 0 0;
                            }      
                        }
                        .name {
                            color: var(--ztc-text-text-2);
                            font-family: var(--ztc-family-font1);
                            font-size: var(--ztc-font-size-font-s20);
                            font-style: normal;
                            font-weight: var(--ztc-weight-semibold);
                            line-height: 20px;
                            display: block;
                            transition: all .4s;           
                        }
                    }
                }
                .reply {
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 16px;
                    text-transform: capitalize;  
                    display: inline-block;
                    transition: all .4s;
                    @media #{$xs} {
                        margin-top: 12px;
                    }
                    i {
                        margin: 0 2px 0 0;
                    }          
                }
              }
        }
        .contact-boxarea {
            border-radius: 8px;
            background: var(--Gray-Color, #EFF1FF);
            padding: 24px 28px; 
            h3 {
                color: var(--ztc-text-text-2);
                font-family: var(--ztc-family-font1);
                font-size: var(--ztc-font-size-font-s24);
                font-style: normal;
                font-weight: var(--ztc-weight-semibold);
                line-height: 24px; 
            }
            .input-area {
                margin-top: 16px;
                input {
                    width: 100%;
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    padding: 20px;
                    border-radius: 4px;
                    background: var(--ztc-bg-bg-1);
                    &::placeholder {
                        color: var(--ztc-text-text-3);
                        font-family: var(--ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                    }              
                }

                textarea {
                    width: 100%;
                    color: var(--ztc-text-text-2);
                    font-family: var(--ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s16);
                    font-style: normal;
                    font-weight: var(--ztc-weight-medium);
                    line-height: 16px;
                    padding: 20px;
                    border-radius: 4px;
                    background: var(--ztc-bg-bg-1);
                    height: 120px;
                    &::placeholder {
                        color: var(--ztc-text-text-3);
                        font-family: var(--ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                    }              
                }
                button {
                    border: none;
                    outline: none;
                    width: 100%;
                    text-align: center;
                }
            }
        }
    }
}

.vl-blog-bottom-area {
    position: relative;
    z-index: 1;
    .vl-blog-1-item {
        position: relative;
        z-index: 1;
        overflow: hidden;
        border-radius: 16px;
        margin-bottom: 30px;
        &:hover {
            .vl-blog-1-thumb {
                img {
                    transform: scale(1.1) rotate(-4deg);
                    transition: all .4s;
                    filter: grayscale(1);
                }
            }
        }
        .vl-blog-1-thumb {
            overflow: hidden;
            border-radius: 8px;
            transition: all .4s;
            position: relative;
            img {
                height: 100%;
                width: 100%;
                object-fit: cover;
                border-radius: 8px;
                transition: all .4s;
            }
        }
        .vl-blog-1-content {
            position: relative;
            z-index: 2;
            padding: 24px;
            border-radius: 8px;
            background: #FFF;
            border: 1px solid rgba(170, 170, 170, 0.09); 
            margin: -100px 16px 0;
            ul {
                li {
                    display: inline-block;
                    a {
                        color: #2E0797 ;
                        font-family: var(---ztc-family-font1);
                        font-size: var(--ztc-font-size-font-s16);
                        font-style: normal;
                        font-weight: var(--ztc-weight-medium);
                        line-height: 16px;
                        display: inline-block;
                        transition: all .4s; 
                        border-radius: 4px;
                        background: rgba(111, 105, 247, 0.20);
                        padding: 6px 10px;                       
                        img {
                            margin: -5px 4px 0 0;
                        } 
                        span {
                            color: #E6E6EB;
                            display: inline-block;
                            margin: 0 8px 0;
                        }                     
                    }
                }
            }
            h4  {
                a {
                    color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s20);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 30px;
                    display: inline-block;
                    transition: all .4s; 
                    &:hover {
                        color: #2E0797;
                        transition: all .4s;
                    }                  
                }
            }
            .readmore {
                color: var(--ztc-text-text-2);
                    font-family: var(---ztc-family-font1);
                    font-size: var(--ztc-font-size-font-s18);
                    font-style: normal;
                    font-weight: var(--ztc-weight-semibold);
                    line-height: 18px;
                    display: inline-block;
                    transition: all .4s; 
                    i {
                        margin-left: 4px;
                        transform: rotate(-45deg);
                        transition: all .4s;
                    }
                    &:hover {
                        color: #2E0797;
                        transition: all .4s;
                    } 
            }
        }
    }
}
/*============= BLOG CSS AREA ENDS ===============*/