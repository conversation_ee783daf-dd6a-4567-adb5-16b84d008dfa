import { SpeedInsights } from "@vercel/speed-insights/next"
import { Analytics } from "@vercel/analytics/next"
import AppProvidersWrapper from '@/components/wrappers/AppProvidersWrapper';
import { Figtree, Nunito_Sans } from 'next/font/google';
import TopBar from '@/components/layout/TopBar';
import Footer from '@/components/layout/Footer/Footer';
import CTA from '@/app/(pages)/components/CTA';
import 'aos/dist/aos.css';
import 'slick-carousel/slick/slick-theme.css';
import 'slick-carousel/slick/slick.css';
import 'bootstrap/dist/css/bootstrap.min.css';
import '@/assets/scss/main.scss';
const Nunito = Nunito_Sans({
  display: 'swap',
  style: ['normal'],
  subsets: ['latin'],
  weight: ['200', '300', '400', '500', '600', '700', '800', '900', '1000']
});
const Figtrees = Figtree({
  display: 'swap',
  style: ['normal'],
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700', '800', '900']
});
export const metadata = {
  title: 'WebCircel Technology - Technology & It Solutions Services',
};
export default function RootLayout({
  children
}) {
  return <html lang="en">
      <body className={`${Nunito.className} ${Figtrees.className}`}>
        <AppProvidersWrapper>
          <TopBar />
          {children}
            <CTA />
          <Footer />
          <SpeedInsights />
          <Analytics />
        </AppProvidersWrapper>
      </body>
    </html>;
}