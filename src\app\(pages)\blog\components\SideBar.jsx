import img21 from '@/assets/img/all-images/blog/blog-img21.png';
import img22 from '@/assets/img/all-images/blog/blog-img22.png';
import img23 from '@/assets/img/all-images/blog/blog-img23.png';
import img24 from '@/assets/img/all-images/blog/blog-img24.png';
import author1 from '@/assets/img/all-images/others/author-img1.png';
import author2 from '@/assets/img/all-images/others/author-img2.png';
import author3 from '@/assets/img/all-images/others/author-img3.png';
import author4 from '@/assets/img/all-images/others/author-img4.png';
import author5 from '@/assets/img/all-images/others/author-img5.png';
import author6 from '@/assets/img/all-images/others/author-img6.png';
import author7 from '@/assets/img/all-images/others/author-img7.png';
import author8 from '@/assets/img/all-images/others/author-img8.png';
import calender3 from '@/assets/img/icons/calender3.svg';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Form } from 'react-bootstrap';
import { FaAngleRight, FaMagnifyingGlass } from 'react-icons/fa6';
const avatar = [{
  image: author1
}, {
  image: author2
}, {
  image: author3
}, {
  image: author4
}];
const avatar2 = [{
  image: author5
}, {
  image: author6
}, {
  image: author7
}, {
  image: author8
}];
const link = [{
  title: 'Cyber Security Solution',
  url: '#'
}, {
  title: 'Scalable Cloud Solution',
  url: '#'
}, {
  title: 'Data Protection Services',
  url: '#'
}, {
  title: 'Optimization Management',
  url: '#'
}, {
  title: 'HelpDesk 360 Solutions',
  url: '#'
}, {
  title: 'Software Development',
  url: '#'
}, {
  title: 'Custom App Development',
  url: '#'
}, {
  title: 'It Infrastructure Networking',
  url: '#'
}, {
  title: 'Business Consulting',
  url: '#'
}];
const blog = [{
  image: img21,
  title: 'Scalable IT solutions tailored to meet Best business needs'
}, {
  image: img22,
  title: 'Business Operations with Tailored Best IT Solutions Designed '
}, {
  image: img23,
  title: 'Navigate Complex A World Technology of with Our IT Solutions'
}, {
  image: img24,
  title: 'Discover the Power Best of Data-Driven Insights Transforming'
}];
const SideBar = () => {
  return <>
      <Col lg={4}>
        <div className="blog-auhtor-details-side">
          <div className="search-area">
            <h3>Search</h3>
            <div className="space24" />
            <Form>
              <input type="text" placeholder="Search..." />
              <button type="submit">
                <FaMagnifyingGlass />
              </button>
            </Form>
          </div>
          <div className="space30" />
          <div className="category-list-area">
            <h3>Blog Category</h3>
            <div className="space10" />
            <ul className="p-0">
              {link.map((item, idx) => <li key={idx}>
                  <a href={item.url}>
                    {item.title}
                    <span>
                      <FaAngleRight />
                    </span>
                  </a>
                </li>)}
            </ul>
          </div>
          <div className="space30" />
          <div className="tags-area">
            <h3>Popular Tags</h3>
            <div className="space8" />
            <ul className="p-0 m-0">
              <li>
                <Link href="">#TechSolution</Link>
              </li>&nbsp;
              <li>
                <Link href="">#CloudComputing</Link>
              </li>
            </ul>
            <ul className="p-0 m-0">
              <li>
                <Link href="">#ManagedServices</Link>
              </li>&nbsp;
              <li>
                <Link href="">#BusinessIT</Link>
              </li>
            </ul>
            <ul className="p-0 m-0">
              <li>
                <Link href="">#DataSecurity</Link>
              </li>&nbsp;
              <li>
                <Link href="">#TechForBusiness</Link>
              </li>
            </ul>
            <ul className="p-0 m-0">
              <li>
                <Link href="">#TechUpgrades</Link>
              </li>&nbsp;
              <li>
                <Link href="">#ITInfrastructure</Link>
              </li>
            </ul>
          </div>
          <div className="space30" />
          <div className="recent-posts-area">
            <h3>Latest Added Blog</h3>
            <div className="space24" />
            {blog.map((item, idx) => <div key={idx} className="recent-posts" style={{
            marginBottom: '30px'
          }}>
                <div className="img1">
                  <Image src={item.image} alt="" />
                </div>
                <div className="content">
                  <ul className="p-0">
                    <li>
                      <Link href="">
                        <Image src={calender3} alt="" />4 August 2024
                      </Link>
                    </li>
                  </ul>
                  <h4>
                    <Link href="/blog-single">{item.title}</Link>
                  </h4>
                </div>
              </div>)}
          </div>
          <div className="space30" />
          <div className="auhtor-area">
            <h3>Our Author</h3>
            <div className="space24" />
            <ul className="p-0 m-0">
              {avatar.map((item, idx) => <li key={idx}>
                  <Link href="">
                    <Image src={item.image} alt="" />
                  </Link>
                </li>)}
            </ul>
            <div className="space16" />
            <ul className="p-0 m-0">
              {avatar2.map((item, idx) => <li key={idx}>
                  <Link href="">
                    <Image src={item.image} alt="" />
                  </Link>
                </li>)}
            </ul>
          </div>
        </div>
      </Col>
    </>;
};
export default SideBar;