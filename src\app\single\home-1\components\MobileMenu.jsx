import logo1 from '@/assets/img/logo/logo1.png';
import useActiveSection from '@/hook/useActiveSection';
import Image from 'next/image';
import Link from 'next/link';
import { FaEnvelope, FaFacebookF, FaInstagram, FaLinkedinIn, FaLocationDot, FaPhone, FaTwitter, FaXmark } from 'react-icons/fa6';
const sectionIds = ['about', 'service', 'work', 'case', 'testimonial', 'blog'];
const MenuItem = () => {
  const activeSection = useActiveSection(sectionIds);
  return <>
      <ul className="m-0 p-0 nav-pills">
        {sectionIds.map(id => <li key={id} className="nav-item">
            <a href={`#${id}`} className={`nav-link ${activeSection === id ? 'active' : ''}`}>
              <span>{id.charAt(0).toUpperCase() + id.slice(1)}</span>
            </a>
          </li>)}
      </ul>
    </>;
};
const MobileMenu = ({
  openMobilMenu,
  toggleMobilMenu
}) => {
  return <>
      <div className={`vl-offcanvas ${openMobilMenu ? 'vl-offcanvas-open' : ''}`}>
        <div className="vl-offcanvas-wrapper">
          <div className="vl-offcanvas-header d-flex justify-content-between align-items-center mb-90">
            <div className="vl-offcanvas-logo">
              <Link href="/">
                <Image src={logo1} alt="" />
              </Link>
            </div>
            <div className="vl-offcanvas-close">
              <button onClick={toggleMobilMenu} className="vl-offcanvas-close-toggle">
                <FaXmark className="fa-solid" />
              </button>
            </div>
          </div>
          <div className="vl-offcanvas-menu d-lg-none mb-40">
            <nav>
              <MenuItem />
            </nav>
          </div>
          <div className="space20" />
          <div className="vl-offcanvas-info">
            <h3 className="vl-offcanvas-sm-title">Contact Us</h3>
            <div className="space20" />
            <span>
              <Link href="">
                <FaEnvelope className="fa-regular" /> +57 9954 6476
              </Link>
            </span>
            <span>
              <Link href="">
                <FaPhone className="fa-solid" /> <EMAIL>
              </Link>
            </span>
            <span>
              <Link href="">
                <FaLocationDot className="fa-solid" /> Bhemeara,Kushtia
              </Link>
            </span>
          </div>
          <div className="space20" />
          <div className="vl-offcanvas-social">
            <h3 className="vl-offcanvas-sm-title">Follow Us</h3>
            <div className="space20" />
            <Link href="">
              <FaFacebookF className="fab" />
            </Link>
            <Link href="">
              <FaTwitter className="fab" />
            </Link>
            <Link href="">
              <FaLinkedinIn className="fab" />
            </Link>
            <Link href="">
              <FaInstagram className="fab" />
            </Link>
          </div>
        </div>
      </div>
      <div className={`vl-offcanvas-overlay ${openMobilMenu ? 'vl-offcanvas-overlay-open' : ''}`} />
    </>;
};
export default MobileMenu;