'use client';

import demo1 from '@/assets/img/all-images/demo/demo-img1.png';
import demo2 from '@/assets/img/all-images/demo/demo-img2.png';
import demo3 from '@/assets/img/all-images/demo/demo-img3.png';
import demo4 from '@/assets/img/all-images/demo/demo-img4.png';
import demo5 from '@/assets/img/all-images/demo/demo-img5.png';
import logo6 from '@/assets/img/logo/logo6.png';
import MobileMenu from '@/components/layout/TopBar/components/MobileMenu';
import useScrollEvent from '@/hook/useScrollEvent';
import useToggle from '@/hook/useToggle';
import Image from 'next/image';
import Link from 'next/link';
import { Col, Container, Row } from 'react-bootstrap';
import { FaAngleDown, FaAngleRight, FaArrowRight, FaBarsStaggered } from 'react-icons/fa6';
const Header = () => {
  const {
    scrollY
  } = useScrollEvent();
  const {
    isOpen: openMobilMenu,
    toggle: toggleMobilMenu
  } = useToggle();
  return <>
      <header className="homepage5-body">
        <div id="vl-header-sticky" className={`vl-header-area vl-transparent-header header-${scrollY > 100 && 'sticky'}`}>
          <Container fluid className="headerfix">
            <Row className="align-items-center row-bg4">
              <Col lg={2} md={6} xs={6}>
                <div className="vl-logo">
                  <Link href="/">
                    <Image src={logo6} alt="" />
                  </Link>
                </div>
              </Col>
              <Col lg={7} className="d-none d-lg-block">
                <div className="vl-main-menu text-center">
                  <nav className="vl-mobile-menu-active">
                    <ul className="p-0 m-0">
                      <li className="has-dropdown">
                        <Link href="#">
                          Home
                          <span>
                            <FaAngleDown className="fa-solid fa-angle-down d-lg-inline d-none" />
                          </span>
                        </Link>
                        <div className="vl-mega-menu">
                          <div className="vl-home-menu">
                            <Row className="gx-3 row-cols-1 row-cols-md-1 row-cols-lg-5">
                              <Col>
                                <div className="vl-home-thumb">
                                  <div className="img1">
                                    <Image src={demo1} alt="" />
                                  </div>
                                  <Link href="/">Web Circle Technology  - Homepage 01</Link>
                                  <div className="btn-area1">
                                    <Link href="/" className="vl-btn6">
                                      Multi Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                    <div className="space16" />
                                    <Link href="/single/home-1" target="_blank" className="vl-btn6">
                                      One Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                  </div>
                                  <div className="space20 d-lg-none d-block" />
                                </div>
                              </Col>
                              <Col>
                                <div className="vl-home-thumb">
                                  <div className="img1">
                                    <Image src={demo2} alt="" />
                                  </div>
                                  <Link href="/multi/home-2">Web Circle Technology  - Homepage 02</Link>
                                  <div className="btn-area1">
                                    <Link href="/multi/home-2" className="vl-btn6">
                                      Multi Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                    <div className="space16" />
                                    <Link href="/single/home-2" target="_blank" className="vl-btn6">
                                      One Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                  </div>
                                  <div className="space20 d-lg-none d-block" />
                                </div>
                              </Col>
                              <Col>
                                <div className="vl-home-thumb">
                                  <div className="img1">
                                    <Image src={demo3} alt="" />
                                  </div>
                                  <Link href="/multi/home-3">Web Circle Technology  - Homepage 03</Link>
                                  <div className="btn-area1">
                                    <Link href="/multi/home-3" className="vl-btn6">
                                      Multi Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                    <div className="space16" />
                                    <Link href="/single/home-3" target="_blank" className="vl-btn6">
                                      One Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                  </div>
                                  <div className="space20 d-lg-none d-block" />
                                </div>
                              </Col>
                              <Col>
                                <div className="vl-home-thumb">
                                  <div className="img1">
                                    <Image src={demo4} alt="" />
                                  </div>
                                  <Link href="/multi/home-4">Web Circle Technology  - Homepage 04</Link>
                                  <div className="btn-area1">
                                    <Link href="/multi/home-4" className="vl-btn6">
                                      Multi Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                    <div className="space16" />
                                    <Link href="/single/home-4" target="_blank" className="vl-btn6">
                                      One Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                  </div>
                                  <div className="space20 d-lg-none d-block" />
                                </div>
                              </Col>
                              <Col>
                                <div className="vl-home-thumb">
                                  <div className="img1">
                                    <Image src={demo5} alt="" />
                                  </div>
                                  <Link href="/multi/home-5">Web Circle Technology  - Homepage 05</Link>
                                  <div className="btn-area1">
                                    <Link href="/multi/home-5" className="vl-btn6">
                                      Multi Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                    <div className="space16" />
                                    <Link href="/single/home-5" target="_blank" className="vl-btn6">
                                      One Page
                                      <span className="arrow1">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                      <span className="arrow2">
                                        <FaArrowRight className="fa-solid" />
                                      </span>
                                    </Link>
                                  </div>
                                </div>
                              </Col>
                            </Row>
                          </div>
                        </div>
                      </li>

                      <li className="has-dropdown">
                        <Link href="">
                          Pages
                          <span>
                            <FaAngleDown className="fa-solid d-lg-inline d-none" />
                          </span>
                        </Link>
                        <ul className="sub-menu">
                          <li>
                            <Link href="/about">About Us</Link>
                          </li>
                          <li>
                            <Link href="/team">Our Team</Link>
                          </li>
                          <li>
                            <Link href="/testimonial">Testimonials</Link>
                          </li>
                          <li>
                            <Link href="/contact">Contact Us</Link>
                          </li>
                          <li>
                            <Link href="/faq">FAQ,s</Link>
                          </li>
                          <li>
                            <Link href="/error">404</Link>
                          </li>
                        </ul>
                      </li>
                      <li>
                        <Link href="">
                          Services
                          <span>
                            <FaAngleDown className="fa-solid d-lg-inline d-none" />
                          </span>
                        </Link>
                        <ul className="sub-menu">
                          <li>
                            <Link href="/service">Our Service</Link>
                          </li>
                          <li>
                            <Link href="" className="span-arrow">
                              Service Details
                              <span>
                                <FaAngleRight className="fa-solid d-lg-block d-none" />
                              </span>
                            </Link>
                            <ul className="sub-menu menu1">
                              <li>
                                <Link href="/service-left">Service Left</Link>
                              </li>
                              <li>
                                <Link href="/service-right">Service RIght</Link>
                              </li>
                              <li>
                                <Link href="/service-single">Service Single</Link>
                              </li>
                            </ul>
                          </li>
                        </ul>
                      </li>
                      <li>
                        <Link href="">
                          Case Study
                          <span>
                            <FaAngleDown className="fa-solid d-lg-inline d-none" />
                          </span>
                        </Link>
                        <ul className="sub-menu">
                          <li>
                            <Link href="/case">Case Study</Link>
                          </li>
                          <li>
                            <Link href="" className="span-arrow">
                              Case Details
                              <span>
                                <FaAngleRight className="fa-solid d-lg-block d-none" />
                              </span>
                            </Link>
                            <ul className="sub-menu menu1">
                              <li>
                                <Link href="/case-left">Case Study Left</Link>
                              </li>
                              <li>
                                <Link href="/case-right">Case Study Right</Link>
                              </li>
                              <li>
                                <Link href="/case-single">Case Study Single</Link>
                              </li>
                            </ul>
                          </li>
                        </ul>
                      </li>
                      <li>
                        <Link href="">
                          Blogs
                          <span>
                            <FaAngleDown className="fa-solid d-lg-inline d-none" />
                          </span>
                        </Link>
                        <ul className="sub-menu">
                          <li>
                            <Link href="" className="span-arrow">
                              Our Blogs
                              <span>
                                <FaAngleRight className="fa-solid d-lg-block d-none" />
                              </span>
                            </Link>
                            <ul className="sub-menu menu1">
                              <li>
                                <Link href="/blog1">Blog One</Link>
                              </li>
                              <li>
                                <Link href="/blog2">Blog Two</Link>
                              </li>
                            </ul>
                          </li>
                          <li>
                            <Link href="" className="span-arrow">
                              Blog Sidebar
                              <span>
                                <FaAngleRight className="fa-solid d-lg-block d-none" />
                              </span>
                            </Link>
                            <ul className="sub-menu menu1">
                              <li>
                                <Link href="/blogv1">Blog Left V1</Link>
                              </li>
                              <li>
                                <Link href="/blogv2">Blog Left V2</Link>
                              </li>
                              <li>
                                <Link href="/blog-rightv1">Blog Right V1</Link>
                              </li>
                              <li>
                                <Link href="/blog-rightv2">Blog Right V2</Link>
                              </li>
                            </ul>
                          </li>
                          <li>
                            <Link href="" className="span-arrow">
                              Blog Details
                              <span>
                                <FaAngleRight className="fa-solid d-lg-block d-none" />
                              </span>
                            </Link>
                            <ul className="sub-menu menu1">
                              <li>
                                <Link href="/blog">Blog Left</Link>
                              </li>
                              <li>
                                <Link href="/blog-right">Blog Right</Link>
                              </li>
                              <li>
                                <Link href="/blog-single">Blog Single</Link>
                              </li>
                            </ul>
                          </li>
                        </ul>
                      </li>
                      <li>
                        <Link href="/contact">Contact</Link>
                      </li>
                    </ul>
                  </nav>
                </div>
              </Col>
              <Col lg={3} md={6} xs={6}>
                <div className="vl-hero-btn d-none d-lg-block text-end">
                  <span className="vl-btn-wrap text-end">
                    <Link className="vl-btn6" href="/contact">
                      Let’s Build Your App
                      <span className="arrow1">
                        <FaArrowRight className="fa-solid" />
                      </span>
                      <span className="arrow2">
                        <FaArrowRight className="fa-solid" />
                      </span>
                    </Link>
                  </span>
                </div>
                <div className="vl-header-action-item d-block d-lg-none">
                  <button onClick={toggleMobilMenu} type="button" className="vl-offcanvas-toggle">
                    <FaBarsStaggered />
                  </button>
                </div>
              </Col>
            </Row>
          </Container>
        </div>
        <MobileMenu toggleMobilMenu={toggleMobilMenu} openMobilMenu={openMobilMenu} />
      </header>
    </>;
};
export default Header;